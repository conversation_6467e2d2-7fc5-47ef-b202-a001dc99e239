console.log('pay')
class Pay {
  static isPaying = false;
  constructor() {
    console.log('pay初始化')
  }
  getFirstProductStatus() {
    fetch('https://logs.uggamer.com/fish_clash/product/getProductFirstPayInfo?account=' + localStorage.getItem('account_num'), {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).then((res) => {
      console.log(res, 'datadata')
      if(res.code === 200) {
        window.firstProductStatus = res.data.map(item=>{
          return {
            product_id: item.id,
            is_product_first_pay: item.productFirstCharge
          }
        })
        window.checkFirstProductStatus = (product_id) => {
          return window.firstProductStatus.find(item=>item.product_id === String(product_id)).is_product_first_pay === 1 ? true : false
        }
      }
    }).catch((err) => {
      console.log(err)
    })
  }
  async pay(product_id) {
    if (this.isPaying) {
      return
    }
    this.isPaying = true
    console.log('pay', product_id)
    const res = await fetch('https://logs.uggamer.com/order/createOrderId?productId=' + product_id + '&userId=' + localStorage.getItem('account_num'), {
      method: 'get',
      headers: {
        'Content-Type': 'application/json'
      }
    }).then((res) => {
      return res.json()
    }).catch((err) => {
      console.log(err)
    })
    console.log('pay', res)
    if (res.code === 200) {
      logReport('ugd_purchase_should', {
        product_id,
        price: res.data[0].price
      })
      return new Promise((resolve, reject) => {
        if (window.flutter_inappwebview) {
          // window.flutterObj.googlePay('com.ol.fishstorm.survival.io.shelltype6', res.data[0].orderId).then(async (googleRes) => {
          window.flutterObj.googlePay(res.data[0].googleProductId, res.data[0].orderId).then(async (googleRes) => {
            console.log(googleRes, 'googleRes')
            logReport('ugd_purchase_suc')
            this.getFirstProductStatus()
          }).catch((err) => {
            logReport('ugd_purchase_fail')
            reject(err)
          }).finally(async () => {
            this.isPaying = false
            const res1 = await fetch('https://logs.uggamer.com/product/checkProductPayResult?orderId=' + res.data[0].orderId, {
              method: 'get',
              headers: {
                'Content-Type': 'application/json'
              }
            }).then((resLog) => {
              return resLog.json()
            }).catch((err) => {
              console.log(err)
            })
            console.log(res1, 'res1')
            if (res1.data.orderStatus === 2) {
              logReport('ugd_purchase', {
                product_id,
                price: res1.data.price,
                purchase_state: true,
                extra_reward: res1.data.productFirstCharge,
                first_pay: res1.data.gameFirstCharge
              })
            } else {
              logReport('ugd_purchase', {
                product_id,
                price: res1.data.price,
                purchase_state: false,
                extra_reward: res1.data.productFirstCharge,
                first_pay: res1.data.gameFirstCharge
              })
            }
          })
        } else {
          resolve(true)
        }
      })
    } else {
      this.isPaying = false
      return Promise.reject(res)
    }
  }
}

window.payObj = new Pay()