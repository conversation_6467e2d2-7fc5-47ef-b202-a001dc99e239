window.flutterObj.getInfoSystemInfo().then((res)=>{
  if(res) {
    window.systemInfo = res
  }
})
const logReport = (type, data) => {
  console.log('report', type, data)
  let event_name = ''
  const event_data = {}
  switch (type) {
    case 'CmdBattleResultReqMsg':
      event_name = 'ugd_level'
      if(data.star) {
        event_data.state = 'pass'
        event_data.star = data.star < 60 ? 1 : data.star < 100 ? 2 : 3;
      } else {
        event_data.state = 'fail'
        event_data.star = 0;
      }
      event_data.level = data.level
      event_data.kill_number = data.killNumber
      event_data.hp = data.hp
      event_data.time = data.battleTime
      console.log('ugd_level', event_data)
      break
    case 'CmdBattleSweepReqMsg':
      event_name = 'ugd_level_sweep'
      event_data.level = data.level
      console.log('ugd_level_sweep')
      break
    case 'CmdBattleCompleteInfoReqMsg':
      event_name = 'ugd_level_preview'
      event_data.level = data.level
      console.log('ugd_level_preview')
      break
    case 'CmdBattleSelectSkillReqMsg':
      event_name = 'ugd_skill'
      event_data.level = data.level
      event_data.skill_id = data.id
      /**
       * 数组，转盘转到的多个技能也是这个事件
       */
      console.log('ugd_skill')
      break
    case 'CmdCastleSkillUpLevelReqMsg':
      event_name = 'ugd_skill_level_up'
      event_data.skill_id = data.skillId
      console.log('ugd_skill_level_up')
      break
    case 'CmdEquipUpLevelReqMsg':
      event_name = 'ugd_equip_level_up'
      event_data.equip_id = data.id
      event_data.equip_level_up_all = Boolean(data.type)
      console.log('ugd_equip_level_up')
      break
    case 'CmdGemComposeReqMsg':
      event_name = 'ugd_gem_compose'
      event_data.gem_id = data.map(item=>item.resId).join(',')
      console.log(event_data.gem_id)
      console.log('ugd_gem_compose')
      break
    case 'CmdGemDownReqMsg':
      event_name = 'ugd_gem_down'
      event_data.equip_id = data.equipId
      event_data.page_id = data.page
      console.log('ugd_gem_down')
      break
    case 'CmdGemOnReqMsg':
      event_name = 'ugd_gem_on'
      event_data.equip_id = data.equipId
      event_data.page_id = data.page
      event_data.gem_id = data.id
      console.log('ugd_gem_on')
      break
    case 'CmdGemRefineReqMsg':
      event_name = 'ugd_gem_refine'
      event_data.gem_id = data.id
      console.log('ugd_gem_refine')
      break
    case 'CmdGemReplaceReqMsg':
      event_name = 'ugd_gem_replace'
      event_data.gem_id = data.id
      event_data.equip_id = data.equipId
      event_data.page_id = data.page
      console.log('ugd_gem_replace')
      break
    case 'CmdRoleChangeNameReqMsg':
      event_name = 'ugd_role_name_change'
      event_data.name = data.name
      console.log('ugd_role_name_change')
      break
    case 'CmdBattleTokenTakeRewardReqMsg':
      event_name = 'ugd_battle_token'
      event_data.take_all = data.takeAll
      event_data.level = data.level
      event_data.token_level = data.resId // 等级123
      console.log('ugd_battle_token')
      break
    case 'CmdMiningBuyReqMsg':
      event_name = 'ugd_pigbank'
      console.log('ugd_pigbank')
      break
    case 'CmdSigninTakeTaskRewardReqMsg':
      const str = data.resId + ''
      if(str.includes('10401')) {
        event_name = 'ugd_daily_reward'
        event_data.daily_day = Number(str.slice(5, 7)) % 7
        event_data.totel_day = Number(str.slice(5, 7))
      } else if(str.includes('10400')) {
        event_name = 'ugd_daily_reward_total'
        event_data.totel_reward = Number(str.slice(5, 7)) * 7
      }
      // event_data.totel_day = data.resId // 1040115  后两位是天数
      // event_data.totel_reward = data.resId // 1040001 后两位是7：1 14:2 21:3 28:4
      console.log('ugd_daily_reward')
      break
    case 'CmdSevenDayTakeTaskRewardReqMsg':
      event_name = 'ugd_seven_day_reward'
      event_data.totel_day = Number((data.resId + '').slice(4)) // 10001  后两位是天数
      console.log('ugd_seven_day_reward')
      break
    case 'CmdShopBuyReqMsg':
      event_name = 'ugd_shop_buy'
      event_data.shop_type = data.cmdShopType
      event_data.item_id = data.id
      event_data.ten_time = Boolean(data.tenTimes)
      console.log('ugd_shop_buy')
      break
    case 'ugd_register':
      event_name = 'ugd_register'
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_register')
      break
    case 'firstLogin':
      event_name = 'ugd_first_login'
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_first_login', window.__roleID)
      break
    case 'login':
      event_name = 'ugd_game_login'
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_game_login')
      break
    case 'ugd_Interstitial_should':
      event_name = 'ugd_Interstitial_should'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_Interstitial_should')
      break
    case 'ugd_Interstitial_play':
      event_name = 'ugd_Interstitial'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_Interstitial')
      break
    case 'ugd_Interstitial_click':
      event_name = 'ugd_Interstitial_click'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_Interstitial_click')
      break
    case 'ugd_Interstitial_over':
      event_name = 'ugd_Interstitial_over'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_Interstitial_over')
      break
    case 'ugd_rv_should':
      event_name = 'ugd_rv_should'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_rv_should')
      break
    case 'ugd_rv_play':
      event_name = 'ugd_rv'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_rv_play')
      break
    case 'ugd_rv_click':
      event_name = 'ugd_rv_click'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_rv_click')
      break
    case 'ugd_rv_over':
      event_name = 'ugd_rv_over'
      event_data.ad_id = data.ad_id
      event_data.location = data.location
      console.log('ugd_rv_over')
      break
    case 'ugd_purchase_should':
      event_name = 'ugd_purchase_should'
      event_data.order_id = data.product_id
      event_data.price = data.price
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_purchase_should')
      break
    case 'ugd_purchase':
      event_name = 'ugd_purchase'
      event_data.product_id = data.product_id
      event_data.price = data.price
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_purchase')
      break
    case 'ugd_purchase_fail':
      event_name = 'ugd_purchase_fail'
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_purchase_fail')
      break
    case 'ugd_purchase_suc':
      event_name = 'ugd_purchase_cancel'
      event_data.user_uid = window.__roleID
      event_data.user_account = localStorage.getItem('account_num')
      console.log('ugd_purchase_cancel')
      break
  }
  if(!event_name) return
  const globalData = {
    app_id: 'fish_clash_test',
    distinct_id: window.__roleID+'',
    client_time: new Date().toISOString(),
    country: "",
    country_code: "",
    os: window.systemInfo?.osName || "",
    device_id: window.systemInfo?.deviceId || "",
    app_version: window.systemInfo?.appVersion || "",
    internal_version: window.systemInfo?.appVersion || "",
    system_language: window.systemInfo?.appLanguage || "zh",
    utm: new URLSearchParams(window.location.search).get('utm_source') || "",
    account_id: localStorage.getItem('account_num'),
  }
  fetch('https://logs.uggamer.com/log/api/v1/log', {
    method: 'POST',
    mode: 'cors', // 明确指定CORS模式
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': 'zhxmvt91duwcvwef'
    },
    body: JSON.stringify({
      ...globalData,
      event_name,
      event_content: JSON.stringify(event_data)
    })
  });
}