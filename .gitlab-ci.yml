# This file is a template, and might need editing before it works on your project.
# This is a sample GitLab CI/CD configuration file that should run without any modifications.
# It demonstrates a basic 3 stage CI/CD pipeline. Instead of real tests or scripts,
# it uses echo commands to simulate the pipeline execution.
#
# A pipeline is composed of independent jobs that run scripts, grouped into stages.
# Stages run in sequential order, but jobs within stages run in parallel.
#
# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages
#
# You can copy and paste this template into a new `.gitlab-ci.yml` file.
# You should not add this template to an existing `.gitlab-ci.yml` file by using the `include:` keyword.
#
# To contribute improvements to CI/CD templates, please follow the Development guide at:
# https://docs.gitlab.com/ee/development/cicd/templates.html
# This specific template is located at:
# https://gitlab.com/gitlab-org/gitlab/-/blob/master/lib/gitlab/ci/templates/Getting-Started.gitlab-ci.yml
variables:
  GIT_STRATEGY: clone
  GIT_DEPTH: 0

stages:      
  - deploy
  - notify

deploy-job:      # This job runs in the deploy stage.
  stage: deploy  # It only runs when *both* jobs in the test stage complete successfully.
  only:
    - dev
  script:
    - whoami
    - cd /www/wwwroot/xian-test.uggamer.com/pmrx-game/
    - sudo git pull

notify-success:
  stage: notify
  only:
    - dev
  when: on_success
  script:
    - curl -X POST 
      -H "Content-Type:application/json" 
      -d '{
      "msgtype":"markdown",
      "markdown":{
      "content":"'"$CI_PROJECT_NAME"'-'"$CI_COMMIT_REF_NAME"'分支部署成功(test服)\n>推送用户:'"$CI_COMMIT_AUTHOR"'\n>推送时间：'"$CI_JOB_STARTED_AT"'\n>提交信息：'"$CI_COMMIT_MESSAGE"'\n"
      }
      }' 
      https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=83a71840-7d54-4785-99af-a6bbf3bed887

notify-failure:
  stage: notify
  only:
    - dev
  when: on_failure
  script:
    - |
      # 获取最后一个命令的退出状态码
      FAILURE_REASON="Exit code: $?"
      curl -X POST 
      -H "Content-Type:application/json" 
      -d '{
      "msgtype":"markdown",
      "markdown":{
      "content":"'"$CI_PROJECT_NAME"'-'"$CI_COMMIT_REF_NAME"'分支部署失败(test服)\n>推送用户:'"$CI_COMMIT_AUTHOR"'\n>推送时间：'"$CI_JOB_STARTED_AT"'\n>提交信息：'"$CI_COMMIT_MESSAGE"'\n>失败原因：'"$FAILURE_REASON"'\n>作业状态：'"$CI_JOB_STATUS"'\n"
      }
      }' 
      https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=83a71840-7d54-4785-99af-a6bbf3bed887